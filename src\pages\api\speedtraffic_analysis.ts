import { NextApiRequest, NextApiResponse } from 'next';

// 심볼별 뮤텍스 - 동시 요청 방지
const processing = new Map<string, { active: boolean; startTime: number }>();

// 서킷 브레이커 패턴 - 심볼별 실패 추적
const failureCount = new Map<string, number>();
const lastFailureTime = new Map<string, number>();
const FAILURE_THRESHOLD = 3;
const CIRCUIT_BREAKER_TIMEOUT = 5 * 60 * 1000; // 5분

// 오래된 처리 항목 정리
const cleanupStaleProcessing = () => {
  const now = Date.now();
  const twoMinutes = 2 * 60 * 1000;
  let cleanedCount = 0;

  for (const [symbol, info] of processing.entries()) {
    if (info.active && (now - info.startTime) > twoMinutes) {
      console.log(`[SPEEDTRAFFIC_API] 오래된 처리 항목 정리: ${symbol}`);
      processing.delete(symbol);
      cleanedCount++;
    }
  }

  if (cleanedCount > 0) {
    console.log(`[SPEEDTRAFFIC_API] 정리 완료: ${cleanedCount}개 항목 제거`);
  }
};

// 1분마다 정리 작업 실행
setInterval(cleanupStaleProcessing, 60 * 1000);

// 서킷 브레이커 상태 확인
const isCircuitBreakerOpen = (symbol: string): boolean => {
  const failures = failureCount.get(symbol) || 0;
  const lastFailure = lastFailureTime.get(symbol) || 0;
  const now = Date.now();

  if (failures >= FAILURE_THRESHOLD) {
    if (now - lastFailure < CIRCUIT_BREAKER_TIMEOUT) {
      return true;
    } else {
      failureCount.delete(symbol);
      lastFailureTime.delete(symbol);
      return false;
    }
  }
  return false;
};

// 실패 기록
const recordFailure = (symbol: string) => {
  const failures = (failureCount.get(symbol) || 0) + 1;
  failureCount.set(symbol, failures);
  lastFailureTime.set(symbol, Date.now());
  console.log(`[SPEEDTRAFFIC_API] 실패 기록 ${failures}회: ${symbol}`);
};

// 성공 시 실패 카운터 리셋
const recordSuccess = (symbol: string) => {
  failureCount.delete(symbol);
  lastFailureTime.delete(symbol);
};

// Vercel 서버리스 함수 호출
const callVercelAPI = async (endpoint: string, symbol: string): Promise<any> => {
  try {
    const baseUrl = process.env.VERCEL_URL 
      ? `https://${process.env.VERCEL_URL}`
      : 'http://localhost:3000';
    
    const url = `${baseUrl}/api/${endpoint}?symbol=${encodeURIComponent(symbol)}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30초 타임아웃
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`[${endpoint}] API 호출 실패:`, error);
    throw error;
  }
};

// 통합 분석 실행
const runIntegratedAnalysis = async (symbol: string) => {
  const results: any = {};
  
  // 분석 엔드포인트들
  const endpoints = [
    'mfi_analysis',
    'rsi_analysis', 
    'bollinger_analysis',
    'capm_analysis',
    'garch_analysis',
    'industry_analysis'
  ];

  // 병렬로 모든 분석 실행
  const promises = endpoints.map(async (endpoint) => {
    try {
      const result = await callVercelAPI(endpoint, symbol);
      return { endpoint, result };
    } catch (error) {
      console.error(`[${endpoint}] 분석 실패:`, error);
      return { 
        endpoint, 
        result: { 
          error: error instanceof Error ? error.message : 'Unknown error',
          traffic_light: 'red' 
        } 
      };
    }
  });

  // 모든 결과 수집
  const analysisResults = await Promise.all(promises);
  
  // 결과 정리
  for (const { endpoint, result } of analysisResults) {
    const key = endpoint.replace('_analysis', '');
    results[key] = result;
  }

  // 신호등 상태 결정
  const trafficLights = determineTrafficLights(results);

  return {
    symbol,
    timestamp: new Date().toISOString(),
    ...results,
    traffic_lights: trafficLights
  };
};

// 신호등 상태 결정
const determineTrafficLights = (results: any) => {
  const getColor = (key: string) => results[key]?.traffic_light || 'red';
  
  // 기술적 분석 (MFI + RSI + Bollinger)
  const technicalColors = [
    getColor('mfi'),
    getColor('rsi'), 
    getColor('bollinger')
  ];
  
  const technical = aggregateColors(technicalColors);
  
  return {
    technical,
    industry: getColor('industry'),
    market: getColor('capm'),
    risk: getColor('garch')
  };
};

// 색상 집계
const aggregateColors = (colors: string[]): string => {
  const greenCount = colors.filter(c => c === 'green').length;
  const redCount = colors.filter(c => c === 'red').length;
  
  if (greenCount >= 2) return 'green';
  if (redCount >= 2) return 'red';
  return 'yellow';
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { symbol } = req.query;

  if (!symbol || typeof symbol !== 'string') {
    return res.status(400).json({ error: 'Symbol parameter is required' });
  }

  const ticker = symbol.toUpperCase();

  // 서킷 브레이커 확인
  if (isCircuitBreakerOpen(ticker)) {
    console.log(`[SPEEDTRAFFIC_API] 서킷 브레이커 열림: ${ticker}`);
    return res.status(503).json({ 
      error: 'Service temporarily unavailable due to repeated failures',
      retry_after: Math.ceil(CIRCUIT_BREAKER_TIMEOUT / 1000)
    });
  }

  // 동시 요청 방지
  if (processing.has(ticker)) {
    const info = processing.get(ticker)!;
    const elapsed = Date.now() - info.startTime;
    console.log(`[SPEEDTRAFFIC_API] 이미 처리 중: ${ticker} (${elapsed}ms 경과)`);
    return res.status(429).json({ 
      error: 'Analysis already in progress for this symbol',
      elapsed_ms: elapsed
    });
  }

  // 처리 시작 표시
  processing.set(ticker, { active: true, startTime: Date.now() });

  try {
    console.log(`[SPEEDTRAFFIC_API] 분석 시작: ${ticker}`);
    const startTime = Date.now();

    // 통합 분석 실행
    const result = await runIntegratedAnalysis(ticker);

    const executionTime = Date.now() - startTime;
    console.log(`[SPEEDTRAFFIC_API] 분석 완료: ${ticker} (${executionTime}ms)`);

    // 성공 기록
    recordSuccess(ticker);

    res.status(200).json(result);

  } catch (error) {
    const executionTime = Date.now() - (processing.get(ticker)?.startTime || Date.now());
    console.error(`[SPEEDTRAFFIC_API] 분석 실패: ${ticker} (${executionTime}ms)`, error);

    // 실패 기록
    recordFailure(ticker);

    res.status(500).json({
      error: 'Analysis failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      execution_time_ms: executionTime
    });

  } finally {
    // 처리 완료 표시
    processing.delete(ticker);
  }
}
