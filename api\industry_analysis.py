from http.server import BaseHTTPRequestHandler
import json
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
import urllib.parse
import numpy as np

class handler(BaseHTTPRequestHandler):
    def do_GET(self):
        try:
            # URL 파라미터 파싱
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            
            # 심볼 추출
            symbol = query_params.get('symbol', [None])[0]
            if not symbol:
                self.send_error_response(400, "Symbol parameter is required")
                return
            
            symbol = symbol.upper()
            
            # 산업 분석 계산
            result = self.calculate_industry_analysis(symbol)
            
            # 성공 응답
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
            
            self.wfile.write(json.dumps(result).encode())
            
        except Exception as e:
            self.send_error_response(500, f"Internal server error: {str(e)}")
    
    def do_OPTIONS(self):
        # CORS preflight 요청 처리
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def send_error_response(self, status_code, message):
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        error_response = {"error": message}
        self.wfile.write(json.dumps(error_response).encode())
    
    def load_stock_data(self, symbol):
        """주식 데이터 로드"""
        try:
            # 1년간의 데이터 가져오기
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)
            
            ticker_obj = yf.Ticker(symbol)
            hist = ticker_obj.history(start=start_date, end=end_date)
            
            if hist.empty:
                raise ValueError(f"No data available for {symbol}")
            
            # 컬럼명 표준화
            hist.columns = [col.replace(' ', '').title() for col in hist.columns]
            hist.rename(columns={'Adjclose': 'AdjClose'}, inplace=True)
            
            return hist
            
        except Exception as e:
            raise Exception(f"Failed to load data for {symbol}: {str(e)}")
    
    def get_industry_benchmark(self, symbol):
        """산업 벤치마크 심볼 반환 (간단한 매핑)"""
        # 간단한 산업 매핑 (실제로는 더 복잡한 로직 필요)
        industry_mapping = {
            # 기술주
            'AAPL': '^IXIC',  # 나스닥
            'MSFT': '^IXIC',
            'GOOGL': '^IXIC',
            'TSLA': '^IXIC',
            # 한국 주식은 KOSPI 사용
            'default': '^KS11'  # KOSPI
        }
        
        # 한국 주식인지 확인 (간단한 방법)
        if symbol.endswith('.KS') or len(symbol) == 6:
            return '^KS11'  # KOSPI
        
        return industry_mapping.get(symbol, '^KS11')
    
    def calculate_industry_analysis(self, symbol):
        """산업 분석 계산"""
        # 개별 종목 데이터 로드
        stock_data = self.load_stock_data(symbol)
        
        # 산업 벤치마크 데이터 로드
        benchmark_symbol = self.get_industry_benchmark(symbol)
        benchmark_data = self.load_stock_data(benchmark_symbol)
        
        # 필요한 컬럼이 있는지 확인
        if 'Close' not in stock_data.columns or 'Close' not in benchmark_data.columns:
            raise ValueError("Missing 'Close' column in data")
        
        # 데이터 정리 및 정렬
        stock_data = stock_data.dropna()
        benchmark_data = benchmark_data.dropna()
        
        # 공통 날짜 인덱스로 맞추기
        common_dates = stock_data.index.intersection(benchmark_data.index)
        if len(common_dates) < 60:  # 최소 60일 데이터 필요
            raise ValueError(f"Insufficient overlapping data: {len(common_dates)} days")
        
        stock_prices = stock_data.loc[common_dates, 'Close']
        benchmark_prices = benchmark_data.loc[common_dates, 'Close']
        
        # 일일 수익률 계산
        stock_returns = stock_prices.pct_change().dropna()
        benchmark_returns = benchmark_prices.pct_change().dropna()
        
        # 공통 날짜로 다시 맞추기
        common_return_dates = stock_returns.index.intersection(benchmark_returns.index)
        stock_returns = stock_returns.loc[common_return_dates]
        benchmark_returns = benchmark_returns.loc[common_return_dates]
        
        if len(stock_returns) < 30:
            raise ValueError(f"Insufficient return data: {len(stock_returns)} days")
        
        # 베타 계산 (공분산 / 벤치마크 분산)
        covariance = np.cov(stock_returns, benchmark_returns)[0, 1]
        benchmark_variance = np.var(benchmark_returns)
        
        if benchmark_variance == 0:
            raise ValueError("Benchmark variance is zero")
        
        beta = covariance / benchmark_variance
        
        # R-squared 계산
        correlation = np.corrcoef(stock_returns, benchmark_returns)[0, 1]
        r_squared = correlation ** 2
        
        # t-통계량 계산 (간단한 버전)
        n = len(stock_returns)
        se_beta = np.sqrt((1 - r_squared) / (n - 2)) * np.sqrt(np.var(stock_returns) / np.var(benchmark_returns))
        t_stat = beta / se_beta if se_beta != 0 else 0
        
        # 신호등 색상 결정
        if beta > 1.3:
            color = "red"          # 산업 대비 고위험
            signal = "산업 대비 고위험"
            summary_ko = f"산업 베타가 {beta:.2f}로 산업 평균보다 높은 변동성을 보입니다."
        elif beta < 0.7:
            color = "green"        # 산업 대비 저위험
            signal = "산업 대비 저위험"
            summary_ko = f"산업 베타가 {beta:.2f}로 산업 평균보다 낮은 변동성을 보입니다."
        else:
            color = "yellow"       # 산업 평균
            signal = "산업 평균"
            summary_ko = f"산업 베타가 {beta:.2f}로 산업 평균과 비슷한 변동성을 보입니다."
        
        return {
            "symbol": symbol,
            "date": common_return_dates[-1].date().isoformat(),
            "beta": round(float(beta), 4),
            "r2": round(float(r_squared), 4),
            "tstat": round(float(t_stat), 4),
            "benchmark": benchmark_symbol,
            "traffic_light": color,
            "signal": signal,
            "summary_ko": summary_ko,
            "timestamp": datetime.now().isoformat()
        }
