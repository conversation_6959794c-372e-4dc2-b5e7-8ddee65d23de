// 주식 데이터 로딩 유틸리티

import { StockData } from './types';

/**
 * Yahoo Finance API를 통해 주식 데이터를 가져옵니다
 */
export async function fetchStockData(symbol: string, years: number = 3): Promise<StockData[]> {
  try {
    // 한국 주식의 경우 .KS 접미사 추가
    const yahooSymbol = symbol.includes('.') ? symbol : `${symbol}.KS`;

    const endDate = Math.floor(Date.now() / 1000);
    const startDate = endDate - (years * 365 * 24 * 60 * 60);

    // realtime_chart_data.ts와 동일한 API 엔드포인트 사용
    const url = `https://query1.finance.yahoo.com/v8/finance/chart/${yahooSymbol}?period1=${startDate}&period2=${endDate}&interval=1d`;

    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return parseYahooChartData(data, yahooSymbol);

  } catch (error) {
    console.error(`Failed to fetch data for ${symbol}:`, error);
    throw new Error(`주식 데이터 로딩 실패: ${symbol} - ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * KOSPI 지수 데이터를 가져옵니다
 */
export async function fetchKOSPIData(years: number = 3): Promise<StockData[]> {
  return fetchStockData('^KS11', years);
}

/**
 * Yahoo Chart API 응답을 StockData 배열로 파싱합니다
 */
function parseYahooChartData(data: any, symbol: string): StockData[] {
  if (!data.chart || !data.chart.result || data.chart.result.length === 0) {
    throw new Error(`No data found for ${symbol}`);
  }

  const result = data.chart.result[0];
  const timestamps = result.timestamp;
  const quote = result.indicators.quote[0];

  const closes = quote.close;
  const opens = quote.open;
  const highs = quote.high;
  const lows = quote.low;
  const volumes = quote.volume;

  if (!timestamps || !closes) {
    throw new Error(`Invalid data structure for ${symbol}`);
  }

  const stockData: StockData[] = [];

  for (let i = 0; i < timestamps.length; i++) {
    const timestamp = timestamps[i];
    const open = opens?.[i];
    const high = highs?.[i];
    const low = lows?.[i];
    const close = closes[i];
    const volume = volumes?.[i] || 0;

    // null 값 체크
    if (close !== null && close !== undefined &&
        open !== null && open !== undefined &&
        high !== null && high !== undefined &&
        low !== null && low !== undefined) {

      const date = new Date(timestamp * 1000).toISOString().split('T')[0];

      stockData.push({
        date,
        open,
        high,
        low,
        close,
        volume
      });
    }
  }

  return stockData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
}

/**
 * CSV 텍스트를 StockData 배열로 파싱합니다 (레거시)
 */
function parseCSVData(csvText: string): StockData[] {
  const lines = csvText.trim().split('\n');
  const data: StockData[] = [];

  // 헤더 라인 스킵
  for (let i = 1; i < lines.length; i++) {
    const columns = lines[i].split(',');

    if (columns.length >= 6) {
      const [date, open, high, low, close, , volume] = columns;

      // 유효한 숫자 데이터인지 확인
      const openNum = parseFloat(open);
      const highNum = parseFloat(high);
      const lowNum = parseFloat(low);
      const closeNum = parseFloat(close);
      const volumeNum = parseInt(volume) || 0;

      if (!isNaN(openNum) && !isNaN(highNum) && !isNaN(lowNum) && !isNaN(closeNum)) {
        data.push({
          date,
          open: openNum,
          high: highNum,
          low: lowNum,
          close: closeNum,
          volume: volumeNum
        });
      }
    }
  }

  return data.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
}



/**
 * 수익률 계산
 */
export function calculateReturns(prices: number[]): number[] {
  const returns: number[] = [];
  
  for (let i = 1; i < prices.length; i++) {
    const returnRate = (prices[i] - prices[i - 1]) / prices[i - 1];
    returns.push(returnRate);
  }
  
  return returns;
}

/**
 * 이동평균 계산
 */
export function calculateMovingAverage(values: number[], period: number): number[] {
  const ma: number[] = [];
  
  for (let i = period - 1; i < values.length; i++) {
    const sum = values.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
    ma.push(sum / period);
  }
  
  return ma;
}

/**
 * 표준편차 계산
 */
export function calculateStandardDeviation(values: number[], period: number): number[] {
  const std: number[] = [];
  
  for (let i = period - 1; i < values.length; i++) {
    const slice = values.slice(i - period + 1, i + 1);
    const mean = slice.reduce((a, b) => a + b, 0) / slice.length;
    const variance = slice.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / slice.length;
    std.push(Math.sqrt(variance));
  }
  
  return std;
}
