import React from 'react';

interface TrafficLight {
  id: string;
  label: string;
  status: 'inactive' | 'red' | 'yellow' | 'green';
  description: string;
  icon: React.ReactNode;
}

interface SpeedTrafficLightsProps {
  technicalLight: 'inactive' | 'red' | 'yellow' | 'green';
  industryLight: 'inactive' | 'red' | 'yellow' | 'green';
  marketLight: 'inactive' | 'red' | 'yellow' | 'green';
  riskLight: 'inactive' | 'red' | 'yellow' | 'green';
  isLoading?: boolean;
}

const SpeedTrafficLights: React.FC<SpeedTrafficLightsProps> = ({
  technicalLight,
  industryLight,
  marketLight,
  riskLight,
  isLoading = false
}) => {
  const getTrafficLightColor = (status: 'inactive' | 'red' | 'yellow' | 'green') => {
    switch (status) {
      case 'red': return 'bg-red-500 shadow-red-500/50';
      case 'yellow': return 'bg-yellow-500 shadow-yellow-500/50';
      case 'green': return 'bg-green-500 shadow-green-500/50';
      default: return 'bg-gray-600 shadow-gray-600/30';
    }
  };

  const getStatusText = (status: 'inactive' | 'red' | 'yellow' | 'green') => {
    switch (status) {
      case 'red': return '매도 신호';
      case 'yellow': return '관망 신호';
      case 'green': return '매수 신호';
      default: return '분석 중...';
    }
  };

  const getStatusIcon = (status: 'inactive' | 'red' | 'yellow' | 'green') => {
    switch (status) {
      case 'red':
        return (
          <svg className="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m0 0l7-7m-7 7h14" />
          </svg>
        );
      case 'yellow':
        return (
          <svg className="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        );
      case 'green':
        return (
          <svg className="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m0 0l-7 7m7-7H3" />
          </svg>
        );
      default:
        return (
          <svg className="w-6 h-6 text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        );
    }
  };

  const lights: TrafficLight[] = [
    {
      id: 'technical',
      label: '기술적 분석',
      status: technicalLight,
      description: '차트 패턴 및 기술 지표 분석',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      )
    },
    {
      id: 'industry',
      label: '산업 분석',
      status: industryLight,
      description: '업종 및 동종업계 비교 분석',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      )
    },
    {
      id: 'market',
      label: '시장 분석',
      status: marketLight,
      description: '전체 시장 동향 및 거시경제 분석',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    {
      id: 'risk',
      label: '리스크 분석',
      status: riskLight,
      description: '변동성 및 위험도 평가',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
      )
    }
  ];

  // 전체 투자 신호 계산
  const calculateOverallSignal = () => {
    const signals = [technicalLight, industryLight, marketLight, riskLight];
    const activeSignals = signals.filter(signal => signal !== 'inactive');
    
    if (activeSignals.length === 0) return 'analyzing';
    
    const greenCount = activeSignals.filter(signal => signal === 'green').length;
    const redCount = activeSignals.filter(signal => signal === 'red').length;
    const yellowCount = activeSignals.filter(signal => signal === 'yellow').length;
    
    if (greenCount >= 3) return 'strong_buy';
    if (greenCount >= 2 && redCount <= 1) return 'buy';
    if (redCount >= 3) return 'strong_sell';
    if (redCount >= 2 && greenCount <= 1) return 'sell';
    return 'hold';
  };

  const overallSignal = calculateOverallSignal();

  const getOverallSignalInfo = () => {
    switch (overallSignal) {
      case 'strong_buy':
        return { text: '강력 매수', color: 'text-green-400', bgColor: 'bg-green-500/20', borderColor: 'border-green-500/30' };
      case 'buy':
        return { text: '매수', color: 'text-green-300', bgColor: 'bg-green-500/10', borderColor: 'border-green-500/20' };
      case 'strong_sell':
        return { text: '강력 매도', color: 'text-red-400', bgColor: 'bg-red-500/20', borderColor: 'border-red-500/30' };
      case 'sell':
        return { text: '매도', color: 'text-red-300', bgColor: 'bg-red-500/10', borderColor: 'border-red-500/20' };
      case 'hold':
        return { text: '관망', color: 'text-yellow-400', bgColor: 'bg-yellow-500/20', borderColor: 'border-yellow-500/30' };
      default:
        return { text: '분석 중', color: 'text-gray-400', bgColor: 'bg-gray-500/20', borderColor: 'border-gray-500/30' };
    }
  };

  const signalInfo = getOverallSignalInfo();

  return (
    <div className="space-y-4">
      {/* 종합 신호 - 컴팩트 버전 */}
      <div className={`p-4 rounded-lg border ${signalInfo.bgColor} ${signalInfo.borderColor}`}>
        <div className="text-center">
          <h3 className="text-lg font-bold text-white mb-1">종합 투자 신호</h3>
          <div className={`text-2xl font-bold ${signalInfo.color} mb-1`}>
            {signalInfo.text}
          </div>
          <p className="text-blue-300 text-xs">
            4중 AI 분석 결과
          </p>
        </div>
      </div>

      {/* 신호등 디스플레이 - 컴팩트 버전 */}
      <div className="bg-gray-900 rounded-lg p-4 mx-auto max-w-[140px]">
        <div className="grid grid-rows-4 gap-3">
          {lights.map((light) => (
            <div key={light.id} className="flex items-center space-x-3">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center border border-gray-700 ${getTrafficLightColor(light.status)} shadow-lg transition-all duration-500`}>
                {light.status === 'inactive' && isLoading ? (
                  <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <div className={`w-6 h-6 rounded-full ${
                    light.status === 'green' ? 'bg-green-400 animate-pulse' :
                    light.status === 'yellow' ? 'bg-yellow-400 animate-pulse' :
                    light.status === 'red' ? 'bg-red-400 animate-pulse' : 'bg-gray-500'
                  }`}></div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-xs font-medium text-white truncate">{light.label}</div>
                <div className="text-xs text-gray-300 truncate">{getStatusText(light.status)}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SpeedTrafficLights;
