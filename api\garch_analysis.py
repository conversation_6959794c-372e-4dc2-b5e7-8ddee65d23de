from http.server import BaseHTTPRequestHandler
import json
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
import urllib.parse
import numpy as np
from scipy import stats

class handler(BaseHTTPRequestHandler):
    def do_GET(self):
        try:
            # URL 파라미터 파싱
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            
            # 심볼 추출
            symbol = query_params.get('symbol', [None])[0]
            if not symbol:
                self.send_error_response(400, "Symbol parameter is required")
                return
            
            symbol = symbol.upper()
            
            # 변동성 분석 계산
            result = self.calculate_volatility_analysis(symbol)
            
            # 성공 응답
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
            
            self.wfile.write(json.dumps(result).encode())
            
        except Exception as e:
            self.send_error_response(500, f"Internal server error: {str(e)}")
    
    def do_OPTIONS(self):
        # CORS preflight 요청 처리
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def send_error_response(self, status_code, message):
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        error_response = {"error": message}
        self.wfile.write(json.dumps(error_response).encode())
    
    def load_stock_data(self, symbol):
        """주식 데이터 로드"""
        try:
            # 1년간의 데이터 가져오기
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)
            
            ticker_obj = yf.Ticker(symbol)
            hist = ticker_obj.history(start=start_date, end=end_date)
            
            if hist.empty:
                raise ValueError(f"No data available for {symbol}")
            
            # 컬럼명 표준화
            hist.columns = [col.replace(' ', '').title() for col in hist.columns]
            hist.rename(columns={'Adjclose': 'AdjClose'}, inplace=True)
            
            return hist
            
        except Exception as e:
            raise Exception(f"Failed to load data for {symbol}: {str(e)}")
    
    def calculate_volatility_analysis(self, symbol):
        """변동성 분석 계산 (GARCH 대신 간단한 변동성 모델)"""
        # 데이터 로드
        df = self.load_stock_data(symbol)
        
        # 필요한 컬럼이 있는지 확인
        if 'Close' not in df.columns:
            raise ValueError("Missing 'Close' column in data")
        
        # 데이터 정리
        df = df.dropna()
        if len(df) < 60:  # 최소 60일 데이터 필요
            raise ValueError(f"Insufficient data for volatility calculation: {len(df)} days")
        
        # 데이터 처리
        df = df.sort_index()
        prices = df['Close']
        
        # 일일 수익률 계산
        returns = prices.pct_change().dropna()
        
        if len(returns) < 30:
            raise ValueError(f"Insufficient return data: {len(returns)} days")
        
        # 변동성 계산 (연율화)
        daily_volatility = returns.std()
        annualized_volatility = daily_volatility * np.sqrt(252)  # 252 영업일
        
        # VaR 계산 (Value at Risk)
        var_95 = np.percentile(returns, 5)  # 5% VaR
        var_99 = np.percentile(returns, 1)  # 1% VaR
        
        # 최근 30일 변동성
        recent_returns = returns.tail(30)
        recent_volatility = recent_returns.std() * np.sqrt(252)
        
        # 변동성 비교 (최근 vs 전체)
        volatility_ratio = recent_volatility / annualized_volatility if annualized_volatility > 0 else 1
        
        # 신호등 색상 결정
        if annualized_volatility > 0.4:  # 40% 이상
            color = "red"          # 고위험
            signal = "고위험"
            summary_ko = f"연율화 변동성이 {annualized_volatility*100:.1f}%로 매우 높아 고위험 종목입니다."
        elif annualized_volatility < 0.2:  # 20% 미만
            color = "green"        # 저위험
            signal = "저위험"
            summary_ko = f"연율화 변동성이 {annualized_volatility*100:.1f}%로 낮아 안정적인 종목입니다."
        else:
            color = "yellow"       # 중간위험
            signal = "중간위험"
            summary_ko = f"연율화 변동성이 {annualized_volatility*100:.1f}%로 보통 수준입니다."
        
        return {
            "symbol": symbol,
            "date": df.index[-1].date().isoformat(),
            "sigma_pct": round(float(annualized_volatility * 100), 2),  # 연율화 변동성 (%)
            "var95_pct": round(float(var_95 * 100), 2),  # 95% VaR (%)
            "var99_pct": round(float(var_99 * 100), 2),  # 99% VaR (%)
            "recent_volatility_pct": round(float(recent_volatility * 100), 2),  # 최근 30일 변동성 (%)
            "volatility_ratio": round(float(volatility_ratio), 2),  # 변동성 비율
            "traffic_light": color,
            "signal": signal,
            "summary_ko": summary_ko,
            "timestamp": datetime.now().isoformat()
        }
